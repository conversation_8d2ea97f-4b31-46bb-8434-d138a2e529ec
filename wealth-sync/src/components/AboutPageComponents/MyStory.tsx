import React from "react";

const MyStory = () => {
  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold tracking-tight">My Story</h2>
      <div className="prose max-w-none">
        <p>
          My journey into the world of finance began as a fixed-income trader,
          where I spent years analyzing markets, managing portfolios, and
          executing trades across various financial instruments. During this
          time, I developed a deep understanding of financial markets and
          investment strategies.
        </p>
        <p>
          While I enjoyed the fast-paced environment of trading, I found myself
          increasingly drawn to technology and software development. The
          intersection of finance and technology fascinated me, and I decided to
          pivot my career toward software engineering.
        </p>
        <p>
          Despite changing careers, I remained an active investor, maintaining
          portfolios across multiple platforms including traditional brokerages
          and cryptocurrency exchanges. As my investments diversified, I faced a
          common challenge: keeping track of everything in one place.
        </p>
        <p>
          I found myself juggling between different apps and platforms, manually
          tracking my overall portfolio performance in spreadsheets. This
          fragmented approach was time-consuming and prone to errors. I needed a
          solution that could aggregate all my investment data in one place,
          providing a comprehensive view of my financial position.
        </p>
        <p>
          That&apos;s when the idea for WealthSync was born. I combined my
          financial expertise with my software engineering skills to create a
          platform that solves this exact problem. WealthSync connects to
          multiple investment platforms, aggregates your data, and provides
          powerful analytics to help you make informed decisions.
        </p>
        <p>
          Today, WealthSync is the tool I wish I had when I started my
          investment journey. I&apos;m excited to share it with fellow investors
          who face similar challenges in managing their diversified portfolios.
        </p>
      </div>
    </div>
  );
};

export default MyStory;
